{% extends 'todos/base.html' %}

{% block content %}
<!-- 隐藏的CSRF令牌 -->
{% csrf_token %}

<!-- 页面标题 -->
<div class="app-title">
    <h1 style="font-size: 2.5rem; margin: 0;">✅ 待办事项</h1>
    <p style="margin: 10px 0 0 0; opacity: 0.9;">让生活更有条理</p>
</div>

<!-- 添加任务表单 -->
<div class="add-task-form">
    <form hx-post="{% url 'todos:add_todo' %}"
          hx-target="#todo-list"
          hx-swap="afterbegin"
          hx-on::after-request="this.reset()">
        {% csrf_token %}
        <div class="form-grid">
            {{ form.title }}
            {{ form.priority }}
            {{ form.category }}
            <button type="submit" class="btn-primary">
                <span class="htmx-indicator spinner me-2"></span>
                添加任务
            </button>
        </div>
        <div style="margin-top: 10px;">
            {{ form.description }}
        </div>
    </form>
</div>

<!-- 搜索和过滤栏 -->
<div class="filter-bar">
    <!-- 隐藏的表单用于处理过滤 -->
    <form id="filter-form" style="display: none;"></form>
    
    <div class="search-box">
        <input type="text"
               name="search"
               placeholder="🔍 搜索任务..."
               value="{{ search_query }}"
               hx-get="{% url 'todos:filter_todos' %}"
               hx-target="#todo-list-container"
               hx-trigger="keyup changed delay:500ms"
               hx-swap="innerHTML"
               hx-include="[name='category'], [name='priority'], [name='status']"
               form="filter-form">
    </div>
    
    <div class="filter-options">
        <select name="category" 
                hx-get="{% url 'todos:filter_todos' %}"
                hx-target="#todo-list-container"
                hx-trigger="change"
                hx-swap="innerHTML"
                hx-include="[name='search'], [name='priority'], [name='status']"
                form="filter-form">
            <option value="all" {% if category_filter == 'all' %}selected{% endif %}>所有分类</option>
            <option value="work" {% if category_filter == 'work' %}selected{% endif %}>🏢 工作</option>
            <option value="study" {% if category_filter == 'study' %}selected{% endif %}>📚 学习</option>
            <option value="life" {% if category_filter == 'life' %}selected{% endif %}>🏠 生活</option>
            <option value="other" {% if category_filter == 'other' %}selected{% endif %}>📌 其他</option>
        </select>
        
        <select name="priority" 
                hx-get="{% url 'todos:filter_todos' %}"
                hx-target="#todo-list-container"
                hx-trigger="change"
                hx-swap="innerHTML"
                hx-include="[name='search'], [name='category'], [name='status']"
                form="filter-form">
            <option value="all" {% if priority_filter == 'all' %}selected{% endif %}>所有优先级</option>
            <option value="high" {% if priority_filter == 'high' %}selected{% endif %}>🔴 高</option>
            <option value="medium" {% if priority_filter == 'medium' %}selected{% endif %}>🟡 中</option>
            <option value="low" {% if priority_filter == 'low' %}selected{% endif %}>🟢 低</option>
        </select>
        
        <select name="status" 
                hx-get="{% url 'todos:filter_todos' %}"
                hx-target="#todo-list-container"
                hx-trigger="change"
                hx-swap="innerHTML"
                hx-include="[name='search'], [name='category'], [name='priority']"
                form="filter-form">
            <option value="all" {% if status_filter == 'all' %}selected{% endif %}>所有状态</option>
            <option value="active" {% if status_filter == 'active' %}selected{% endif %}>⏳ 进行中</option>
            <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>✅ 已完成</option>
        </select>
    </div>
</div>

<!-- 统计信息 -->
<div class="stats-container">
    <div class="stat-item">
        <div class="stat-number">{{ stats.total }}</div>
        <div class="stat-label">总任务</div>
    </div>
    <div class="stat-item">
        <div class="stat-number active">{{ stats.active }}</div>
        <div class="stat-label">进行中</div>
    </div>
    <div class="stat-item">
        <div class="stat-number completed">{{ stats.completed }}</div>
        <div class="stat-label">已完成</div>
    </div>
    <div class="stat-item">
        <div class="stat-number high-priority">{{ stats.high_priority }}</div>
        <div class="stat-label">高优先级</div>
    </div>
</div>

<!-- 任务列表容器 -->
<div id="todo-list-container">
    <!-- 任务统计 -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <h3 style="margin: 0; color: #333;">我的任务</h3>
        <span style="background: #667eea; color: white; padding: 5px 15px; border-radius: 20px; font-size: 14px;">
            {{ todos|length }} 个任务
        </span>
    </div>

    <!-- 待办事项列表 -->
    <div id="todo-list">
        {% for todo in todos %}
            {% include 'todos/todo_item.html' %}
        {% empty %}
            <div class="empty-state">
                <div style="font-size: 60px; margin-bottom: 20px;">📝</div>
                <h4 style="margin: 0 0 10px 0;">没有找到任务</h4>
                <p style="margin: 0; opacity: 0.7;">尝试调整搜索条件或添加新任务</p>
            </div>
        {% endfor %}
    </div>
</div>

<!-- 底部提示 -->
<div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef; color: #6c757d; font-size: 14px;">
    <p style="margin: 0;">💡 提示：点击任务可编辑，所有操作都是实时的</p>
</div>

<style>
.add-task-form {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.form-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 10px;
    align-items: end;
}

.filter-bar {
    background: white;
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.search-box {
    flex: 1;
    min-width: 200px;
}

.search-box input {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 14px;
}

.filter-options {
    display: flex;
    gap: 10px;
}

.filter-options select {
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    cursor: pointer;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    background: white;
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-number.active { color: #667eea; }
.stat-number.completed { color: #28a745; }
.stat-number.high-priority { color: #dc3545; }

.stat-label {
    font-size: 12px;
    color: #6c757d;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-bar {
        flex-direction: column;
    }
    
    .filter-options {
        width: 100%;
        flex-wrap: wrap;
    }
    
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}
