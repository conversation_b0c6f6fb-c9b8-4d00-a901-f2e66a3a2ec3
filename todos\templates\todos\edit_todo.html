<div class="todo-card edit-mode" id="todo-{{ todo.id }}">
    <form hx-post="{% url 'todos:edit_todo' todo.id %}"
          hx-target="#todo-{{ todo.id }}"
          hx-swap="outerHTML">
        {% csrf_token %}
        
        <div class="edit-form">
            <div class="form-group">
                <label>标题</label>
                {{ form.title }}
            </div>
            
            <div class="form-group">
                <label>描述</label>
                {{ form.description }}
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>优先级</label>
                    {{ form.priority }}
                </div>
                
                <div class="form-group">
                    <label>分类</label>
                    {{ form.category }}
                </div>
                
                <div class="form-group">
                    <label>截止日期</label>
                    {{ form.due_date }}
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn-primary">
                    <span class="htmx-indicator spinner me-2"></span>
                    保存
                </button>
                <button type="button" 
                        class="btn-cancel"
                        hx-get="{% url 'todos:filter_todos' %}?search=&category=all&priority=all&status=all"
                        hx-target="#todo-{{ todo.id }}"
                        hx-swap="outerHTML">
                    取消
                </button>
            </div>
        </div>
    </form>
</div>

<style>
.edit-mode {
    background: #f8f9fa;
    border-left-color: #667eea !important;
}

.edit-form {
    width: 100%;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-group textarea {
    resize: vertical;
    min-height: 60px;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.btn-cancel {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.3s;
}

.btn-cancel:hover {
    background: #5a6268;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
}
</style>