<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTMX 待办事项</title>
    
    <!-- 使用更轻量的CSS框架 -->
    <link href="https://cdn.jsdelivr.net/npm/water.css@2/out/water.css" rel="stylesheet">
    
    <!-- HTMX库 -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- 内联关键样式 -->
    <style>
        /* 自定义样式 */
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .main-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        
        .completed {
            text-decoration: line-through;
            opacity: 0.6;
            color: #6c757d;
        }
        
        .htmx-indicator {
            opacity: 0;
            transition: opacity 0.2s;
        }
        
        .htmx-request .htmx-indicator {
            opacity: 1;
        }
        
        /* 待办事项卡片样式 */
        .todo-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        
        .todo-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .todo-card.completed {
            border-left-color: #28a745;
            background: #f8f9fa;
        }
        
        /* 输入框样式 */
        .todo-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .todo-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        /* 选择框样式 */
        .todo-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            background: white;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        
        .todo-select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        /* 文本域样式 */
        textarea.todo-input {
            min-height: 80px;
            resize: vertical;
        }
        
        /* 按钮样式 */
        .btn-primary {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-delete:hover {
            background: #c82333;
        }
        
        /* 复选框样式 */
        .todo-checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
            margin-right: 12px;
        }
        
        /* 标题样式 */
        .app-title {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        /* 简单的图标 */
        .icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 8px;
            vertical-align: middle;
        }
        
        /* 加载动画 */
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    
    <!-- HTMX CSRF配置 -->
    <script>
        function getCSRFToken() {
            return document.querySelector('[name=csrfmiddlewaretoken]').value;
        }
        
        document.addEventListener('htmx:configRequest', function(evt) {
            evt.detail.headers['X-CSRFToken'] = getCSRFToken();
        });
    </script>
</head>
<body>
    <div class="main-container">
        {% block content %}
        {% endblock %}
    </div>
</body>
</html>
