<div class="todo-card {% if todo.completed %}completed{% endif %}" id="todo-{{ todo.id }}">
    <div class="todo-content">
        <!-- 左侧：优先级、复选框和任务内容 -->
        <div class="todo-main">
            <!-- 优先级指示器 -->
            <div class="priority-indicator {% if todo.priority == 'high' %}high{% elif todo.priority == 'medium' %}medium{% else %}low{% endif %}">
            </div>
            
            <input type="checkbox"
                   class="todo-checkbox"
                   {% if todo.completed %}checked{% endif %}
                   hx-post="{% url 'todos:toggle_todo' todo.id %}"
                   hx-target="#todo-{{ todo.id }}"
                   hx-swap="outerHTML">
            
            <div class="todo-details">
                <div class="todo-title {% if todo.completed %}completed-text{% endif %}">
                    {{ todo.title }}
                </div>
                
                {% if todo.description %}
                    <div class="todo-description">
                        {{ todo.description }}
                    </div>
                {% endif %}
                
                <div class="todo-meta">
                    <span class="category-badge">
                        {% if todo.category == 'work' %}🏢 工作
                        {% elif todo.category == 'study' %}📚 学习
                        {% elif todo.category == 'life' %}🏠 生活
                        {% else %}📌 其他{% endif %}
                    </span>
                    
                    <span class="created-time">
                        📅 {{ todo.created_at|date:"m-d H:i" }}
                    </span>
                    
                    {% if todo.due_date %}
                        <span class="due-date">
                            ⏰ 截止: {{ todo.due_date|date:"m-d H:i" }}
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- 右侧：状态标签和操作按钮 -->
        <div class="todo-actions">
            <span class="status-badge {% if todo.completed %}completed{% else %}active{% endif %}">
                {% if todo.completed %}✅ 已完成{% else %}⏳ 进行中{% endif %}
            </span>
            
            <div class="action-buttons">
                <button class="btn-edit"
                        hx-get="{% url 'todos:edit_todo' todo.id %}"
                        hx-target="#todo-{{ todo.id }}"
                        hx-swap="outerHTML"
                        title="编辑任务">
                    ✏️
                </button>
                
                <button class="btn-delete"
                        hx-delete="{% url 'todos:delete_todo' todo.id %}"
                        hx-target="#todo-{{ todo.id }}"
                        hx-swap="outerHTML"
                        onclick="return confirm('确定要删除这个任务吗？')"
                        title="删除任务">
                    <span class="htmx-indicator spinner me-1"></span>
                    🗑️
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.todo-content {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 15px;
}

.todo-main {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    flex: 1;
}

.priority-indicator {
    width: 4px;
    border-radius: 2px;
    margin-right: 8px;
}

.priority-indicator.high {
    background: #dc3545;
}

.priority-indicator.medium {
    background: #ffc107;
}

.priority-indicator.low {
    background: #28a745;
}

.todo-details {
    flex: 1;
}

.todo-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 4px;
    line-height: 1.4;
}

.completed-text {
    text-decoration: line-through;
    color: #6c757d;
}

.todo-description {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 8px;
    line-height: 1.4;
}

.todo-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 12px;
}

.category-badge {
    background: #e9ecef;
    color: #495057;
    padding: 2px 8px;
    border-radius: 12px;
}

.created-time, .due-date {
    color: #6c757d;
}

.todo-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
}

.status-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.completed {
    background: #d4edda;
    color: #155724;
}

.status-badge.active {
    background: #fff3cd;
    color: #856404;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.btn-edit {
    background: #6c757d;
    color: white;
    border: none;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.btn-edit:hover {
    background: #5a6268;
}

@media (max-width: 576px) {
    .todo-content {
        flex-direction: column;
    }
    
    .todo-actions {
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
        margin-top: 10px;
    }
    
    .todo-meta {
        flex-direction: column;
        gap: 4px;
    }
}
</style>
