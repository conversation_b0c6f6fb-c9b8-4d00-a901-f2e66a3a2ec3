# Generated by Django 4.2.7 on 2025-07-31 10:01

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ("todos", "0001_initial"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="todo",
            options={
                "ordering": [
                    models.Case(
                        models.When(priority="high", then=1),
                        models.When(priority="medium", then=2),
                        models.When(priority="low", then=3),
                        default=4,
                    ),
                    "-created_at",
                ]
            },
        ),
        migrations.AddField(
            model_name="todo",
            name="category",
            field=models.CharField(
                choices=[
                    ("work", "工作"),
                    ("study", "学习"),
                    ("life", "生活"),
                    ("other", "其他"),
                ],
                default="other",
                max_length=10,
                verbose_name="分类",
            ),
        ),
        migrations.AddField(
            model_name="todo",
            name="description",
            field=models.TextField(blank=True, null=True, verbose_name="详细描述"),
        ),
        migrations.AddField(
            model_name="todo",
            name="due_date",
            field=models.DateTimeField(blank=True, null=True, verbose_name="截止日期"),
        ),
        migrations.AddField(
            model_name="todo",
            name="priority",
            field=models.CharField(
                choices=[
                    ("high", "高优先级"),
                    ("medium", "中优先级"),
                    ("low", "低优先级"),
                ],
                default="medium",
                max_length=10,
                verbose_name="优先级",
            ),
        ),
        migrations.AlterField(
            model_name="todo",
            name="completed",
            field=models.BooleanField(default=False, verbose_name="完成状态"),
        ),
        migrations.AlterField(
            model_name="todo",
            name="created_at",
            field=models.DateTimeField(
                default=django.utils.timezone.now, verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="todo",
            name="title",
            field=models.CharField(max_length=200, verbose_name="标题"),
        ),
    ]
