from django import forms
from .models import Todo

class TodoForm(forms.ModelForm):
    class Meta:
        model = Todo
        fields = ['title', 'description', 'priority', 'category', 'due_date']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'todo-input',
                'placeholder': '输入待办事项标题...'
            }),
            'description': forms.Textarea(attrs={
                'rows': 3,
                'placeholder': '添加详细描述（可选）...',
                'style': 'resize: vertical;'
            }),
            'priority': forms.Select(attrs={
                'class': 'todo-select',
            }),
            'category': forms.Select(attrs={
                'class': 'todo-select',
            }),
            'due_date': forms.DateTimeInput(attrs={
                'type': 'datetime-local',
                'class': 'todo-input',
            }),
        }