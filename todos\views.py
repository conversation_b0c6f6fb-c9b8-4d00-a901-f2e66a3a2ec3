# 导入必要的Django模块
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse
from django.db.models import Q, Case, When, Value, IntegerField
from .models import Todo
from .forms import TodoForm


def index(request):
    """
    主页视图函数
    功能：显示所有待办事项列表，支持搜索和过滤
    """
    # 获取查询参数
    search_query = request.GET.get('search', '')
    category_filter = request.GET.get('category', 'all')
    priority_filter = request.GET.get('priority', 'all')
    status_filter = request.GET.get('status', 'all')
    
    # 构建查询
    todos = Todo.objects.all()
    
    # 搜索功能
    if search_query:
        todos = todos.filter(
            Q(title__icontains=search_query) | 
            Q(description__icontains=search_query)
        )
    
    # 分类过滤
    if category_filter != 'all':
        todos = todos.filter(category=category_filter)
    
    # 优先级过滤
    if priority_filter != 'all':
        todos = todos.filter(priority=priority_filter)
    
    # 状态过滤
    if status_filter == 'active':
        todos = todos.filter(completed=False)
    elif status_filter == 'completed':
        todos = todos.filter(completed=True)
    
    # 添加优先级排序字段
    todos = todos.annotate(
        priority_order=Case(
            When(priority='high', then=Value(1)),
            When(priority='medium', then=Value(2)),
            When(priority='low', then=Value(3)),
            default=Value(4),
            output_field=IntegerField(),
        )
    )
    
    # 获取完整的查询集用于统计
    all_todos = todos
    
    # 限制显示数量并排序
    todos = todos.order_by('priority_order', '-created_at')[:50]
    
    # 统计信息
    stats = {
        'total': all_todos.count(),
        'active': all_todos.filter(completed=False).count(),
        'completed': all_todos.filter(completed=True).count(),
        'high_priority': all_todos.filter(priority='high', completed=False).count(),
    }
    
    context = {
        'todos': todos,
        'stats': stats,
        'search_query': search_query,
        'category_filter': category_filter,
        'priority_filter': priority_filter,
        'status_filter': status_filter,
        'form': TodoForm(),
    }
    
    return render(request, 'todos/index.html', context)


def add_todo(request):
    """
    添加待办事项的HTMX端点
    """
    if request.method == 'POST':
        form = TodoForm(request.POST)
        if form.is_valid():
            todo = form.save()
            return render(request, 'todos/todo_item.html', {'todo': todo})
    return HttpResponse('')


def toggle_todo(request, todo_id):
    """
    切换待办事项完成状态的HTMX端点
    """
    if request.method == 'POST':
        todo = get_object_or_404(Todo, id=todo_id)
        todo.completed = not todo.completed
        todo.save()
        return render(request, 'todos/todo_item.html', {'todo': todo})
    return HttpResponse('Method not allowed', status=405)


def delete_todo(request, todo_id):
    """
    删除待办事项的HTMX端点
    """
    if request.method == 'DELETE':
        todo = get_object_or_404(Todo, id=todo_id)
        todo.delete()
        return HttpResponse('')
    return HttpResponse('Method not allowed', status=405)


def edit_todo(request, todo_id):
    """
    编辑待办事项的HTMX端点
    """
    todo = get_object_or_404(Todo, id=todo_id)
    
    if request.method == 'POST':
        form = TodoForm(request.POST, instance=todo)
        if form.is_valid():
            form.save()
            return render(request, 'todos/todo_item.html', {'todo': todo})
    
    # GET请求返回编辑表单
    form = TodoForm(instance=todo)
    return render(request, 'todos/edit_todo.html', {'form': form, 'todo': todo})


def get_stats(request):
    """
    获取统计信息的HTMX端点
    """
    todos = Todo.objects.all()
    stats = {
        'total': todos.count(),
        'active': todos.filter(completed=False).count(),
        'completed': todos.filter(completed=True).count(),
        'high_priority': todos.filter(priority='high', completed=False).count(),
        'by_category': {
            'work': todos.filter(category='work').count(),
            'study': todos.filter(category='study').count(),
            'life': todos.filter(category='life').count(),
            'other': todos.filter(category='other').count(),
        }
    }
    return render(request, 'todos/stats.html', {'stats': stats})


def filter_todos(request):
    """
    过滤待办事项的HTMX端点
    只返回任务列表部分，用于搜索和过滤
    """
    # 获取查询参数
    search_query = request.GET.get('search', '')
    category_filter = request.GET.get('category', 'all')
    priority_filter = request.GET.get('priority', 'all')
    status_filter = request.GET.get('status', 'all')
    
    # 构建查询
    todos = Todo.objects.all()
    
    # 搜索功能
    if search_query:
        todos = todos.filter(
            Q(title__icontains=search_query) | 
            Q(description__icontains=search_query)
        )
    
    # 分类过滤
    if category_filter != 'all':
        todos = todos.filter(category=category_filter)
    
    # 优先级过滤
    if priority_filter != 'all':
        todos = todos.filter(priority=priority_filter)
    
    # 状态过滤
    if status_filter == 'active':
        todos = todos.filter(completed=False)
    elif status_filter == 'completed':
        todos = todos.filter(completed=True)
    
    # 添加优先级排序字段
    todos = todos.annotate(
        priority_order=Case(
            When(priority='high', then=Value(1)),
            When(priority='medium', then=Value(2)),
            When(priority='low', then=Value(3)),
            default=Value(4),
            output_field=IntegerField(),
        )
    )
    
    # 限制显示数量并排序
    todos = todos.order_by('priority_order', '-created_at')[:50]
    
    return render(request, 'todos/todo_list.html', {'todos': todos})