# 导入Django的模型类和时区工具
from django.db import models
from django.utils import timezone


# 定义优先级选项
PRIORITY_CHOICES = [
    ('high', '高优先级'),
    ('medium', '中优先级'),
    ('low', '低优先级'),
]

# 定义分类选项
CATEGORY_CHOICES = [
    ('work', '工作'),
    ('study', '学习'),
    ('life', '生活'),
    ('other', '其他'),
]


class Todo(models.Model):
    """
    待办事项模型类
    用于存储和管理待办事项的数据结构
    """
    # 待办事项标题，最大长度200字符
    title = models.CharField(max_length=200, verbose_name='标题')
    
    # 完成状态，默认为False（未完成）
    completed = models.BooleanField(default=False, verbose_name='完成状态')
    
    # 创建时间，默认为当前时间（使用时区感知的datetime）
    created_at = models.DateTimeField(default=timezone.now, verbose_name='创建时间')
    
    # 优先级字段
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_CHOICES,
        default='medium',
        verbose_name='优先级'
    )
    
    # 分类字段
    category = models.CharField(
        max_length=10,
        choices=CATEGORY_CHOICES,
        default='other',
        verbose_name='分类'
    )
    
    # 任务描述（可选）
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='详细描述'
    )
    
    # 截止日期（可选）
    due_date = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='截止日期'
    )

    class Meta:
        """
        模型的元数据类
        """
        # 设置默认排序方式：按创建时间降序
        ordering = ['-created_at']

    def __str__(self):
        """
        这个方法的作用是定义模型实例的字符串表示形式。主要用途包括：
        1. 在Django管理后台(Admin)界面中，显示每条记录时会调用这个方法
        2. 在Python交互式命令行中打印对象时会显示这个返回值
        3. 在调试时，打印对象会显示这个返回值，便于识别具体是哪条记录
        4. 在其他需要将对象转换为字符串的场景下使用
        
        例如：当你在Admin后台查看Todo列表时，每行都会显示对应的title，
        而不是显示"Todo object (1)"这样的默认形式
        """
        return self.title
