<div class="stats-container">
    <div class="stat-item">
        <div class="stat-number">{{ stats.total }}</div>
        <div class="stat-label">总任务</div>
    </div>
    <div class="stat-item">
        <div class="stat-number active">{{ stats.active }}</div>
        <div class="stat-label">进行中</div>
    </div>
    <div class="stat-item">
        <div class="stat-number completed">{{ stats.completed }}</div>
        <div class="stat-label">已完成</div>
    </div>
    <div class="stat-item">
        <div class="stat-number high-priority">{{ stats.high_priority }}</div>
        <div class="stat-label">高优先级</div>
    </div>
</div>

<div class="category-stats">
    <h4>分类统计</h4>
    <div class="category-list">
        <div class="category-item">
            <span class="category-icon">🏢</span>
            <span class="category-name">工作</span>
            <span class="category-count">{{ stats.by_category.work }}</span>
        </div>
        <div class="category-item">
            <span class="category-icon">📚</span>
            <span class="category-name">学习</span>
            <span class="category-count">{{ stats.by_category.study }}</span>
        </div>
        <div class="category-item">
            <span class="category-icon">🏠</span>
            <span class="category-name">生活</span>
            <span class="category-count">{{ stats.by_category.life }}</span>
        </div>
        <div class="category-item">
            <span class="category-icon">📌</span>
            <span class="category-name">其他</span>
            <span class="category-count">{{ stats.by_category.other }}</span>
        </div>
    </div>
</div>

<style>
.stats-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    background: white;
    border-radius: 12px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-number.active { color: #667eea; }
.stat-number.completed { color: #28a745; }
.stat-number.high-priority { color: #dc3545; }

.stat-label {
    font-size: 12px;
    color: #6c757d;
}

.category-stats {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.category-stats h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.category-list {
    display: grid;
    gap: 10px;
}

.category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.category-icon {
    font-size: 18px;
    margin-right: 10px;
}

.category-name {
    flex: 1;
    font-size: 14px;
    color: #495057;
}

.category-count {
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>