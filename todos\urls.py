# 导入Django的URL路由模块
from django.urls import path
from . import views

app_name = 'todos'

urlpatterns = [
    # 主页路由
    path('', views.index, name='index'),
    
    # 添加待办事项路由
    path('add/', views.add_todo, name='add_todo'),
    
    # 切换待办事项状态路由
    path('toggle/<int:todo_id>/', views.toggle_todo, name='toggle_todo'),
    
    # 删除待办事项路由
    path('delete/<int:todo_id>/', views.delete_todo, name='delete_todo'),
    
    # 编辑待办事项路由
    path('edit/<int:todo_id>/', views.edit_todo, name='edit_todo'),
    
    # 获取统计信息路由
    path('stats/', views.get_stats, name='get_stats'),
    
    # 过滤待办事项路由
    path('filter/', views.filter_todos, name='filter_todos'),
]
